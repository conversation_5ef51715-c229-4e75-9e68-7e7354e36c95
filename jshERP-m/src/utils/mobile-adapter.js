/**
 * 移动端适配器 - 增强版
 * 用于检测移动设备并提供移动端优化配置
 * 支持设备检测、组件适配、响应式处理等核心功能
 */

class MobileAdapter {
  constructor() {
    this.isMobileDevice = this.detectMobile();
    this.deviceInfo = this.getDeviceInfo();
    this.viewportConfig = this.getViewportConfig();
    this.capabilities = this.getDeviceCapabilities();
    this.performance = this.getPerformanceInfo();
    
    // 事件监听器
    this.listeners = new Map();
    
    // 初始化状态
    this.initialized = false;
    
    // 缓存配置
    this.configCache = new Map();
  }

  /**
   * 检测是否为移动设备 - 增强版
   */
  detectMobile() {
    if (typeof window === 'undefined') return false;
    
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;
    
    // 更精确的移动设备检测
    const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile|mobile|CriOS/i;
    const tabletRegex = /iPad|Android(?!.*Mobile)|Tablet|PlayBook|Silk/i;
    
    // 检查用户代理
    const isMobileUA = mobileRegex.test(userAgent);
    const isTabletUA = tabletRegex.test(userAgent);
    
    // 检查屏幕尺寸
    const screenWidth = window.screen.width;
    const screenHeight = window.screen.height;
    const isMobileScreen = Math.min(screenWidth, screenHeight) <= 768;
    const isTabletScreen = Math.min(screenWidth, screenHeight) > 768 && Math.min(screenWidth, screenHeight) <= 1024;
    
    // 检查触摸支持
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    
    // 检查设备像素比
    const isHighDPI = window.devicePixelRatio > 1;
    
    // 综合判断
    if (isMobileUA && !isTabletUA) return true;
    if (isMobileScreen && isTouchDevice) return true;
    
    return false;
  }

  /**
   * 获取设备信息 - 增强版
   */
  getDeviceInfo() {
    if (typeof window === 'undefined') {
      return {
        type: 'unknown',
        os: 'unknown',
        browser: 'unknown',
        version: 'unknown'
      };
    }

    const userAgent = navigator.userAgent;
    
    // 设备类型检测
    let deviceType = 'desktop';
    let deviceModel = 'unknown';
    
    if (/iPad/i.test(userAgent)) {
      deviceType = 'tablet';
      deviceModel = 'ipad';
    } else if (/iPhone/i.test(userAgent)) {
      deviceType = 'mobile';
      deviceModel = 'iphone';
      // 检测iPhone型号
      if (/iPhone.*OS 1[4-9]|iPhone.*OS [2-9][0-9]/i.test(userAgent)) {
        deviceModel = 'iphone-modern';
      }
    } else if (/iPod/i.test(userAgent)) {
      deviceType = 'mobile';
      deviceModel = 'ipod';
    } else if (/Android.*Mobile/i.test(userAgent)) {
      deviceType = 'mobile';
      deviceModel = 'android-phone';
    } else if (/Android/i.test(userAgent)) {
      deviceType = 'tablet';
      deviceModel = 'android-tablet';
    }

    // 操作系统检测
    let os = 'unknown';
    let osVersion = 'unknown';
    
    if (/iPhone|iPad|iPod/i.test(userAgent)) {
      os = 'ios';
      const match = userAgent.match(/OS (\d+)_(\d+)/);
      if (match) {
        osVersion = `${match[1]}.${match[2]}`;
      }
    } else if (/Android/i.test(userAgent)) {
      os = 'android';
      const match = userAgent.match(/Android (\d+\.?\d*)/);
      if (match) {
        osVersion = match[1];
      }
    } else if (/Windows/i.test(userAgent)) {
      os = 'windows';
    } else if (/Mac/i.test(userAgent)) {
      os = 'macos';
    }

    // 浏览器检测
    let browser = 'unknown';
    let browserVersion = 'unknown';
    
    if (/Chrome/i.test(userAgent) && !/Edge/i.test(userAgent)) {
      browser = 'chrome';
      const match = userAgent.match(/Chrome\/(\d+)/);
      if (match) browserVersion = match[1];
    } else if (/Safari/i.test(userAgent) && !/Chrome/i.test(userAgent)) {
      browser = 'safari';
      const match = userAgent.match(/Version\/(\d+)/);
      if (match) browserVersion = match[1];
    } else if (/Firefox/i.test(userAgent)) {
      browser = 'firefox';
      const match = userAgent.match(/Firefox\/(\d+)/);
      if (match) browserVersion = match[1];
    } else if (/Edge/i.test(userAgent)) {
      browser = 'edge';
      const match = userAgent.match(/Edge\/(\d+)/);
      if (match) browserVersion = match[1];
    }

    return {
      type: deviceType,
      model: deviceModel,
      os,
      osVersion,
      browser,
      browserVersion,
      userAgent,
      // 额外信息
      isRetina: window.devicePixelRatio > 1,
      pixelRatio: window.devicePixelRatio || 1,
      colorDepth: screen.colorDepth,
      screenResolution: `${screen.width}x${screen.height}`
    };
  }

  /**
   * 获取设备能力信息
   */
  getDeviceCapabilities() {
    if (typeof window === 'undefined') {
      return {
        touch: false,
        geolocation: false,
        camera: false,
        vibration: false,
        orientation: false,
        fullscreen: false
      };
    }

    return {
      // 触摸支持
      touch: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
      
      // 地理位置
      geolocation: 'geolocation' in navigator,
      
      // 摄像头
      camera: 'mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices,
      
      // 震动
      vibration: 'vibrate' in navigator,
      
      // 屏幕方向
      orientation: 'orientation' in window || 'onorientationchange' in window,
      
      // 全屏
      fullscreen: 'requestFullscreen' in document.documentElement ||
                 'webkitRequestFullscreen' in document.documentElement ||
                 'mozRequestFullScreen' in document.documentElement,
      
      // 网络状态
      connection: 'connection' in navigator || 'mozConnection' in navigator || 'webkitConnection' in navigator,
      
      // 电池状态
      battery: 'getBattery' in navigator,
      
      // 设备内存
      deviceMemory: 'deviceMemory' in navigator ? navigator.deviceMemory : null,
      
      // 硬件并发
      hardwareConcurrency: 'hardwareConcurrency' in navigator ? navigator.hardwareConcurrency : null,
      
      // 本地存储
      localStorage: typeof Storage !== 'undefined',
      
      // Service Worker
      serviceWorker: 'serviceWorker' in navigator,
      
      // Web Workers
      webWorkers: typeof Worker !== 'undefined',
      
      // WebGL
      webgl: this.checkWebGLSupport(),
      
      // CSS支持
      css: {
        flexbox: this.checkCSSSupport('display', 'flex'),
        grid: this.checkCSSSupport('display', 'grid'),
        transforms: this.checkCSSSupport('transform', 'translateX(1px)'),
        transitions: this.checkCSSSupport('transition', 'all 1s'),
        animations: this.checkCSSSupport('animation', 'none'),
        backdropFilter: this.checkCSSSupport('backdrop-filter', 'blur(1px)')
      }
    };
  }

  /**
   * 检查WebGL支持
   */
  checkWebGLSupport() {
    try {
      const canvas = document.createElement('canvas');
      return !!(window.WebGLRenderingContext && 
               (canvas.getContext('webgl') || canvas.getContext('experimental-webgl')));
    } catch (e) {
      return false;
    }
  }

  /**
   * 检查CSS属性支持
   */
  checkCSSSupport(property, value) {
    if (typeof document === 'undefined') return false;
    
    const element = document.createElement('div');
    element.style[property] = value;
    return element.style[property] === value;
  }

  /**
   * 获取性能信息
   */
  getPerformanceInfo() {
    if (typeof window === 'undefined' || !window.performance) {
      return {
        memory: null,
        timing: null,
        connection: null
      };
    }

    const result = {
      // 内存信息
      memory: window.performance.memory ? {
        usedJSHeapSize: window.performance.memory.usedJSHeapSize,
        totalJSHeapSize: window.performance.memory.totalJSHeapSize,
        jsHeapSizeLimit: window.performance.memory.jsHeapSizeLimit
      } : null,
      
      // 时间信息
      timing: window.performance.timing ? {
        navigationStart: window.performance.timing.navigationStart,
        loadEventEnd: window.performance.timing.loadEventEnd,
        domContentLoadedEventEnd: window.performance.timing.domContentLoadedEventEnd
      } : null,
      
      // 网络连接信息
      connection: navigator.connection || navigator.mozConnection || navigator.webkitConnection || null
    };

    return result;
  }

  /**
   * 获取视口配置 - 增强版
   */
  getViewportConfig() {
    if (typeof window === 'undefined') {
      return {
        width: 375,
        height: 667,
        ratio: 1,
        orientation: 'portrait',
        safeArea: { top: 0, bottom: 0, left: 0, right: 0 }
      };
    }

    const width = window.innerWidth;
    const height = window.innerHeight;
    const orientation = width > height ? 'landscape' : 'portrait';
    
    // 获取安全区域信息
    const safeArea = this.getSafeAreaInsets();
    
    // 计算可用区域
    const availableWidth = width - safeArea.left - safeArea.right;
    const availableHeight = height - safeArea.top - safeArea.bottom;

    return {
      width,
      height,
      availableWidth,
      availableHeight,
      ratio: window.devicePixelRatio || 1,
      orientation,
      safeArea,
      // 断点信息
      breakpoint: this.getBreakpoint(width),
      // 是否为刘海屏
      hasNotch: safeArea.top > 20 || safeArea.bottom > 0,
      // 视口比例
      aspectRatio: width / height
    };
  }

  /**
   * 获取安全区域边距
   */
  getSafeAreaInsets() {
    if (typeof window === 'undefined' || typeof getComputedStyle === 'undefined') {
      return { top: 0, bottom: 0, left: 0, right: 0 };
    }

    const style = getComputedStyle(document.documentElement);
    
    return {
      top: parseInt(style.getPropertyValue('--safe-area-inset-top') || '0', 10),
      bottom: parseInt(style.getPropertyValue('--safe-area-inset-bottom') || '0', 10),
      left: parseInt(style.getPropertyValue('--safe-area-inset-left') || '0', 10),
      right: parseInt(style.getPropertyValue('--safe-area-inset-right') || '0', 10)
    };
  }

  /**
   * 获取响应式断点
   */
  getBreakpoint(width) {
    if (width < 576) return 'xs';
    if (width < 768) return 'sm';
    if (width < 992) return 'md';
    if (width < 1200) return 'lg';
    if (width < 1600) return 'xl';
    return 'xxl';
  }

  /**
   * 获取组件配置 - 增强版
   */
  getComponentConfig(componentType) {
    // 检查缓存
    const cacheKey = `component-${componentType}-${this.isMobile}-${this.viewportConfig.breakpoint}`;
    if (this.configCache.has(cacheKey)) {
      return this.configCache.get(cacheKey);
    }

    const isMobile = this.isMobileDevice;
    const isTablet = this.deviceInfo.type === 'tablet';
    const breakpoint = this.viewportConfig.breakpoint;

    const baseConfig = {
      // 表格配置
      table: {
        size: isMobile ? 'small' : 'default',
        scroll: isMobile ? { x: true } : undefined,
        pagination: {
          size: isMobile ? 'small' : 'default',
          showSizeChanger: !isMobile,
          showQuickJumper: !isMobile && !isTablet,
          showTotal: !isMobile,
          pageSize: isMobile ? 10 : (isTablet ? 15 : 20),
          simple: isMobile && breakpoint === 'xs'
        },
        // 移动端优化
        rowSelection: isMobile ? {
          columnWidth: 40,
          fixed: true
        } : undefined,
        // 响应式列配置
        responsive: true,
        // 虚拟滚动（大数据量时）
        virtual: isMobile && this.performance.memory?.usedJSHeapSize > 50000000
      },

      // 表单配置
      form: {
        layout: isMobile ? 'vertical' : (isTablet ? 'vertical' : 'horizontal'),
        labelCol: isMobile || isTablet ? undefined : { span: 6 },
        wrapperCol: isMobile || isTablet ? undefined : { span: 18 },
        size: isMobile ? 'large' : 'default',
        // 移动端优化
        colon: !isMobile,
        requiredMark: isMobile ? 'optional' : true,
        // 自动聚焦
        autoFocus: isMobile,
        // 滚动到错误字段
        scrollToFirstError: true
      },

      // 按钮配置
      button: {
        size: isMobile ? 'large' : 'default',
        block: isMobile && breakpoint === 'xs',
        // 触摸优化
        style: isMobile ? {
          minHeight: '44px',
          borderRadius: '8px'
        } : undefined
      },

      // 输入框配置
      input: {
        size: isMobile ? 'large' : 'default',
        // 移动端优化
        autoComplete: 'off',
        spellCheck: false,
        // 触摸优化
        style: isMobile ? {
          minHeight: '44px',
          fontSize: '16px' // 防止iOS缩放
        } : undefined
      },

      // 选择器配置
      select: {
        size: isMobile ? 'large' : 'default',
        showSearch: true,
        optionFilterProp: 'children',
        // 移动端优化
        dropdownMatchSelectWidth: !isMobile,
        dropdownStyle: isMobile ? {
          maxHeight: '50vh'
        } : undefined,
        // 虚拟滚动
        virtual: true,
        // 触摸优化
        listHeight: isMobile ? 200 : 256
      },

      // 日期选择器配置
      datePicker: {
        size: isMobile ? 'large' : 'default',
        format: 'YYYY-MM-DD',
        // 移动端优化
        inputReadOnly: isMobile,
        dropdownClassName: isMobile ? 'mobile-date-picker' : undefined,
        // 触摸优化
        style: isMobile ? {
          minHeight: '44px'
        } : undefined
      },

      // 模态框配置
      modal: {
        width: isMobile ? '95%' : (isTablet ? '80%' : 800),
        centered: isMobile || isTablet,
        maskClosable: !isMobile,
        // 移动端优化
        destroyOnClose: true,
        keyboard: !isMobile,
        // 全屏模式
        style: isMobile && breakpoint === 'xs' ? {
          top: 0,
          paddingBottom: 0,
          margin: 0,
          maxWidth: '100vw'
        } : undefined,
        bodyStyle: isMobile ? {
          maxHeight: '70vh',
          overflow: 'auto'
        } : undefined
      },

      // 抽屉配置
      drawer: {
        width: isMobile ? '100%' : (isTablet ? '80%' : 600),
        height: isMobile ? '80%' : undefined,
        placement: isMobile ? 'bottom' : 'right',
        // 移动端优化
        destroyOnClose: true,
        keyboard: !isMobile,
        maskClosable: !isMobile
      },

      // 下拉菜单配置
      dropdown: {
        trigger: isMobile ? ['click'] : ['hover', 'click'],
        placement: isMobile ? 'bottomCenter' : 'bottomLeft',
        // 触摸优化
        overlayStyle: isMobile ? {
          minWidth: '200px'
        } : undefined
      },

      // 工具提示配置
      tooltip: {
        trigger: isMobile ? 'click' : 'hover',
        placement: 'top',
        // 移动端优化
        mouseEnterDelay: isMobile ? 0 : 0.1,
        mouseLeaveDelay: isMobile ? 0.1 : 0.1
      },

      // 步骤条配置
      steps: {
        size: isMobile ? 'small' : 'default',
        direction: isMobile && breakpoint === 'xs' ? 'vertical' : 'horizontal',
        // 移动端优化
        labelPlacement: isMobile ? 'vertical' : 'horizontal'
      },

      // 标签页配置
      tabs: {
        size: isMobile ? 'large' : 'default',
        type: isMobile ? 'line' : 'line',
        // 移动端优化
        tabBarGutter: isMobile ? 0 : undefined,
        // 滚动优化
        tabBarStyle: isMobile ? {
          margin: 0,
          borderBottom: '1px solid #f0f0f0'
        } : undefined
      }
    };

    const result = componentType ? baseConfig[componentType] : baseConfig;

    // 缓存结果
    this.configCache.set(cacheKey, result);

    return result;
  }

  /**
   * 获取样式配置 - 增强版
   */
  getStyleConfig() {
    const cacheKey = `style-${this.isMobile}-${this.viewportConfig.breakpoint}`;
    if (this.configCache.has(cacheKey)) {
      return this.configCache.get(cacheKey);
    }

    const isMobile = this.isMobileDevice;
    const breakpoint = this.viewportConfig.breakpoint;
    const isRetina = this.deviceInfo.isRetina;

    const config = {
      // 间距配置
      spacing: {
        xs: isMobile ? 8 : 4,
        sm: isMobile ? 12 : 8,
        md: isMobile ? 16 : 12,
        lg: isMobile ? 24 : 16,
        xl: isMobile ? 32 : 24,
        xxl: isMobile ? 40 : 32
      },

      // 字体配置
      fontSize: {
        xs: isMobile ? 12 : 10,
        sm: isMobile ? 14 : 12,
        md: isMobile ? 16 : 14,
        lg: isMobile ? 18 : 16,
        xl: isMobile ? 20 : 18,
        xxl: isMobile ? 24 : 20
      },

      // 行高配置
      lineHeight: {
        tight: 1.2,
        normal: 1.5,
        relaxed: 1.8
      },

      // 圆角配置
      borderRadius: {
        sm: isMobile ? 4 : 2,
        md: isMobile ? 6 : 4,
        lg: isMobile ? 8 : 6,
        xl: isMobile ? 12 : 8,
        full: '50%'
      },

      // 阴影配置
      boxShadow: {
        sm: isMobile ? '0 2px 4px rgba(0,0,0,0.1)' : '0 1px 2px rgba(0,0,0,0.1)',
        md: isMobile ? '0 4px 8px rgba(0,0,0,0.15)' : '0 2px 4px rgba(0,0,0,0.1)',
        lg: isMobile ? '0 8px 16px rgba(0,0,0,0.2)' : '0 4px 8px rgba(0,0,0,0.15)',
        xl: isMobile ? '0 12px 24px rgba(0,0,0,0.25)' : '0 8px 16px rgba(0,0,0,0.2)'
      },

      // 触摸目标大小
      touchTarget: {
        min: 44, // 最小触摸目标大小
        recommended: 48 // 推荐触摸目标大小
      },

      // 动画配置
      animation: {
        duration: {
          fast: isMobile ? 200 : 150,
          normal: isMobile ? 300 : 250,
          slow: isMobile ? 500 : 400
        },
        easing: {
          ease: 'cubic-bezier(0.25, 0.1, 0.25, 1)',
          easeIn: 'cubic-bezier(0.42, 0, 1, 1)',
          easeOut: 'cubic-bezier(0, 0, 0.58, 1)',
          easeInOut: 'cubic-bezier(0.42, 0, 0.58, 1)'
        }
      },

      // 断点配置
      breakpoints: {
        xs: 0,
        sm: 576,
        md: 768,
        lg: 992,
        xl: 1200,
        xxl: 1600
      },

      // 容器配置
      container: {
        padding: isMobile ? 16 : 24,
        maxWidth: {
          xs: '100%',
          sm: '540px',
          md: '720px',
          lg: '960px',
          xl: '1140px',
          xxl: '1320px'
        }
      },

      // 网格配置
      grid: {
        columns: 24,
        gutter: isMobile ? 16 : 24,
        // 响应式间距
        gutters: {
          xs: 8,
          sm: 16,
          md: 24,
          lg: 32,
          xl: 40,
          xxl: 48
        }
      },

      // 颜色配置
      colors: {
        primary: '#1890ff',
        success: '#52c41a',
        warning: '#faad14',
        error: '#f5222d',
        // 移动端优化的颜色
        touchHighlight: 'rgba(24, 144, 255, 0.1)',
        divider: isMobile ? '#f0f0f0' : '#e8e8e8'
      },

      // Z-index 层级
      zIndex: {
        dropdown: 1050,
        sticky: 1020,
        fixed: 1030,
        modalBackdrop: 1040,
        modal: 1050,
        popover: 1060,
        tooltip: 1070,
        toast: 1080
      }
    };

    // 缓存结果
    this.configCache.set(cacheKey, config);

    return config;
  }

  /**
   * 设置视口元标签 - 增强版
   */
  setViewportMeta() {
    if (typeof document === 'undefined') return;

    let viewport = document.querySelector('meta[name="viewport"]');
    if (!viewport) {
      viewport = document.createElement('meta');
      viewport.name = 'viewport';
      document.head.appendChild(viewport);
    }

    // 根据设备类型设置不同的视口配置
    if (this.isMobile) {
      if (this.deviceInfo.os === 'ios') {
        // iOS 优化配置
        viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover';
      } else if (this.deviceInfo.os === 'android') {
        // Android 优化配置
        viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, target-densitydpi=device-dpi';
      } else {
        // 通用移动端配置
        viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
      }
    } else {
      // 桌面端配置
      viewport.content = 'width=device-width, initial-scale=1.0';
    }

    // 设置安全区域CSS变量
    this.setSafeAreaCSSVariables();
  }

  /**
   * 设置安全区域CSS变量
   */
  setSafeAreaCSSVariables() {
    if (typeof document === 'undefined') return;

    const root = document.documentElement;

    // 设置安全区域变量
    root.style.setProperty('--safe-area-inset-top', 'env(safe-area-inset-top)');
    root.style.setProperty('--safe-area-inset-bottom', 'env(safe-area-inset-bottom)');
    root.style.setProperty('--safe-area-inset-left', 'env(safe-area-inset-left)');
    root.style.setProperty('--safe-area-inset-right', 'env(safe-area-inset-right)');

    // 设置状态栏高度（iOS）
    if (this.deviceInfo.os === 'ios') {
      root.style.setProperty('--status-bar-height', 'env(safe-area-inset-top)');
    }

    // 设置底部安全区域（iPhone X系列）
    if (this.deviceInfo.model === 'iphone-modern') {
      root.style.setProperty('--home-indicator-height', 'env(safe-area-inset-bottom)');
    }
  }

  /**
   * 添加移动端样式类 - 增强版
   */
  addMobileClass() {
    if (typeof document === 'undefined') return;

    const body = document.body;
    const html = document.documentElement;

    // 清除之前的类
    body.classList.remove('mobile-device', 'tablet-device', 'desktop-device');

    if (this.isMobileDevice) {
      // 基础移动端类
      body.classList.add('mobile-device');
      html.classList.add('mobile-device');

      // 设备类型类
      body.classList.add(`device-${this.deviceInfo.type}`);
      body.classList.add(`device-${this.deviceInfo.model}`);

      // 操作系统类
      body.classList.add(`os-${this.deviceInfo.os}`);
      if (this.deviceInfo.osVersion) {
        body.classList.add(`os-${this.deviceInfo.os}-${this.deviceInfo.osVersion.split('.')[0]}`);
      }

      // 浏览器类
      body.classList.add(`browser-${this.deviceInfo.browser}`);

      // 屏幕方向类
      body.classList.add(`orientation-${this.viewportConfig.orientation}`);

      // 断点类
      body.classList.add(`breakpoint-${this.viewportConfig.breakpoint}`);

      // 特殊特性类
      if (this.deviceInfo.isRetina) {
        body.classList.add('retina-display');
      }

      if (this.viewportConfig.hasNotch) {
        body.classList.add('has-notch');
      }

      if (this.capabilities.touch) {
        body.classList.add('touch-device');
      }

    } else if (this.deviceInfo.type === 'tablet') {
      body.classList.add('tablet-device');
      html.classList.add('tablet-device');
    } else {
      body.classList.add('desktop-device');
      html.classList.add('desktop-device');
    }

    // 能力类
    Object.keys(this.capabilities).forEach(capability => {
      if (this.capabilities[capability]) {
        body.classList.add(`supports-${capability}`);
      }
    });
  }

  /**
   * 事件监听管理
   */
  addEventListener(event, callback, options = {}) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }

    this.listeners.get(event).add(callback);

    if (typeof window !== 'undefined') {
      window.addEventListener(event, callback, options);
    }
  }

  removeEventListener(event, callback) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).delete(callback);
    }

    if (typeof window !== 'undefined') {
      window.removeEventListener(event, callback);
    }
  }

  /**
   * 监听屏幕方向变化
   */
  watchOrientation() {
    const handleOrientationChange = () => {
      setTimeout(() => {
        const oldOrientation = this.viewportConfig.orientation;
        this.viewportConfig = this.getViewportConfig();

        // 更新样式类
        document.body.classList.remove(`orientation-${oldOrientation}`);
        document.body.classList.add(`orientation-${this.viewportConfig.orientation}`);

        // 触发自定义事件
        this.dispatchEvent('orientationchange', {
          orientation: this.viewportConfig.orientation,
          viewport: this.viewportConfig
        });
      }, 100);
    };

    this.addEventListener('orientationchange', handleOrientationChange);
    this.addEventListener('resize', handleOrientationChange);
  }

  /**
   * 监听网络状态变化
   */
  watchConnection() {
    if (!this.capabilities.connection) return;

    const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;

    if (connection) {
      const handleConnectionChange = () => {
        this.dispatchEvent('connectionchange', {
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt,
          saveData: connection.saveData
        });
      };

      connection.addEventListener('change', handleConnectionChange);
    }
  }

  /**
   * 分发自定义事件
   */
  dispatchEvent(eventName, detail = {}) {
    if (typeof window === 'undefined') return;

    const event = new CustomEvent(`mobileadapter:${eventName}`, {
      detail: {
        adapter: this,
        ...detail
      }
    });

    window.dispatchEvent(event);
  }

  /**
   * 工具方法：检查是否为移动设备
   */
  isMobile() {
    return this.isMobileDevice;
  }

  /**
   * 工具方法：检查是否为平板设备
   */
  isTablet() {
    return this.deviceInfo.type === 'tablet';
  }

  /**
   * 工具方法：检查是否为桌面设备
   */
  isDesktop() {
    return this.deviceInfo.type === 'desktop';
  }

  /**
   * 工具方法：检查是否为iOS设备
   */
  isIOS() {
    return this.deviceInfo.os === 'ios';
  }

  /**
   * 工具方法：检查是否为Android设备
   */
  isAndroid() {
    return this.deviceInfo.os === 'android';
  }

  /**
   * 工具方法：检查是否支持触摸
   */
  isTouch() {
    return this.capabilities.touch;
  }

  /**
   * 工具方法：检查是否为Retina屏幕
   */
  isRetina() {
    return this.deviceInfo.isRetina;
  }

  /**
   * 工具方法：检查是否有刘海屏
   */
  hasNotch() {
    return this.viewportConfig.hasNotch;
  }

  /**
   * 工具方法：获取当前断点
   */
  getCurrentBreakpoint() {
    return this.viewportConfig.breakpoint;
  }

  /**
   * 工具方法：检查是否匹配断点
   */
  matchBreakpoint(breakpoint) {
    const breakpoints = ['xs', 'sm', 'md', 'lg', 'xl', 'xxl'];
    const currentIndex = breakpoints.indexOf(this.viewportConfig.breakpoint);
    const targetIndex = breakpoints.indexOf(breakpoint);

    return currentIndex >= targetIndex;
  }

  /**
   * 工具方法：获取适配后的尺寸
   */
  getAdaptiveSize(baseSize, mobileSize = null) {
    if (this.isMobileDevice && mobileSize !== null) {
      return mobileSize;
    }
    return baseSize;
  }

  /**
   * 工具方法：获取适配后的间距
   */
  getAdaptiveSpacing(spacing) {
    const config = this.getStyleConfig();
    return config.spacing[spacing] || spacing;
  }

  /**
   * 工具方法：获取适配后的字体大小
   */
  getAdaptiveFontSize(size) {
    const config = this.getStyleConfig();
    return config.fontSize[size] || size;
  }

  /**
   * 工具方法：清除配置缓存
   */
  clearCache() {
    this.configCache.clear();
  }

  /**
   * 工具方法：刷新适配器状态
   */
  refresh() {
    this.isMobileDevice = this.detectMobile();
    this.deviceInfo = this.getDeviceInfo();
    this.viewportConfig = this.getViewportConfig();
    this.capabilities = this.getDeviceCapabilities();
    this.performance = this.getPerformanceInfo();

    // 清除缓存
    this.clearCache();

    // 更新样式类
    this.addMobileClass();

    // 触发刷新事件
    this.dispatchEvent('refresh', {
      deviceInfo: this.deviceInfo,
      viewportConfig: this.viewportConfig
    });
  }

  /**
   * 初始化移动端适配 - 增强版
   */
  init() {
    if (this.initialized) return;

    // 设置视口
    this.setViewportMeta();

    // 添加样式类
    this.addMobileClass();

    // 监听事件
    this.watchOrientation();
    this.watchConnection();

    // 监听窗口大小变化
    this.addEventListener('resize', () => {
      this.refresh();
    });

    // 监听页面可见性变化
    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', () => {
        if (!document.hidden) {
          // 页面重新可见时刷新状态
          this.refresh();
        }
      });
    }

    // 标记为已初始化
    this.initialized = true;

    // 触发初始化完成事件
    this.dispatchEvent('initialized', {
      adapter: this
    });

    console.log('🚀 Mobile Adapter initialized:', {
      isMobile: this.isMobileDevice,
      deviceType: this.deviceInfo.type,
      os: this.deviceInfo.os,
      browser: this.deviceInfo.browser,
      breakpoint: this.viewportConfig.breakpoint,
      capabilities: Object.keys(this.capabilities).filter(key => this.capabilities[key])
    });
  }

  /**
   * 销毁适配器
   */
  destroy() {
    // 移除所有事件监听器
    this.listeners.forEach((callbacks, event) => {
      callbacks.forEach(callback => {
        this.removeEventListener(event, callback);
      });
    });

    this.listeners.clear();
    this.clearCache();
    this.initialized = false;

    // 触发销毁事件
    this.dispatchEvent('destroyed');
  }
}

// 创建全局实例
const mobileAdapter = new MobileAdapter();

// 自动初始化
if (typeof window !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      mobileAdapter.init();
    });
  } else {
    // 如果DOM已经加载完成，立即初始化
    mobileAdapter.init();
  }
}

// 导出适配器类和实例
export { MobileAdapter };
export default mobileAdapter;

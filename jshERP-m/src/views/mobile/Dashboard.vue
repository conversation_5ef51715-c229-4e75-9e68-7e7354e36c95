<template>
  <div class="mobile-dashboard">
    <!-- 顶部欢迎区域 -->
    <div class="dashboard-header">
      <div class="welcome-section">
        <div class="welcome-text">
          <div class="greeting">{{ welcomeMessage }}</div>
          <div class="date">{{ currentDate }}</div>
        </div>
        <div class="user-avatar">
          <a-avatar size="large" :src="userAvatar" icon="user" />
        </div>
      </div>
    </div>

    <!-- 核心数据卡片 -->
    <div class="dashboard-stats">
      <div class="stats-grid">
        <!-- 今日销售 - 主要数据 -->
        <div class="stat-card stat-card-primary" @click="navigateTo('/mobile/sales')">
          <div class="stat-header">
            <span class="stat-title">今日销售</span>
            <a-icon type="rise" class="trend-icon" />
          </div>
          <div class="stat-value">{{ formatMoney(stats.todaySales) }}</div>
          <div class="stat-subtitle">较昨日 +12.5%</div>
        </div>

        <!-- 今日订单 -->
        <div class="stat-card" @click="navigateTo('/mobile/orders')">
          <div class="stat-header">
            <span class="stat-title">今日订单</span>
            <a-icon type="file-text" class="stat-icon" />
          </div>
          <div class="stat-value">{{ stats.orderCount }}</div>
          <div class="stat-subtitle">笔</div>
        </div>

        <!-- 商品总数 -->
        <div class="stat-card" @click="navigateTo('/mobile/material')">
          <div class="stat-header">
            <span class="stat-title">商品总数</span>
            <a-icon type="shopping" class="stat-icon" />
          </div>
          <div class="stat-value">{{ stats.materialCount }}</div>
          <div class="stat-subtitle">种</div>
        </div>

        <!-- 库存预警 -->
        <div class="stat-card stat-card-warning" @click="navigateTo('/mobile/inventory')">
          <div class="stat-header">
            <span class="stat-title">库存预警</span>
            <a-icon type="exclamation-circle" class="stat-icon" />
          </div>
          <div class="stat-value">{{ stats.lowStockCount }}</div>
          <div class="stat-subtitle">需补货</div>
        </div>
      </div>
    </div>

    <!-- 快捷功能 -->
    <div class="dashboard-actions">
      <div class="section-title">
        <span>快捷功能</span>
        <a-button type="link" size="small" @click="viewAllFunctions">全部</a-button>
      </div>
      <div class="action-grid">
        <div class="action-item" @click="quickAction('pos')">
          <div class="action-icon action-icon-primary">
            <a-icon type="shopping-cart" />
          </div>
          <div class="action-label">POS销售</div>
        </div>

        <div class="action-item" @click="quickAction('purchase')">
          <div class="action-icon action-icon-blue">
            <a-icon type="plus-circle" />
          </div>
          <div class="action-label">采购开单</div>
        </div>

        <div class="action-item" @click="quickAction('inventory')">
          <div class="action-icon action-icon-orange">
            <a-icon type="audit" />
          </div>
          <div class="action-label">库存盘点</div>
        </div>

        <div class="action-item" @click="quickAction('material')">
          <div class="action-icon action-icon-green">
            <a-icon type="plus" />
          </div>
          <div class="action-label">添加商品</div>
        </div>

        <div class="action-item" @click="quickAction('report')">
          <div class="action-icon action-icon-purple">
            <a-icon type="bar-chart" />
          </div>
          <div class="action-label">销售报表</div>
        </div>

        <div class="action-item" @click="quickAction('customer')">
          <div class="action-icon action-icon-cyan">
            <a-icon type="team" />
          </div>
          <div class="action-label">客户管理</div>
        </div>

        <div class="action-item" @click="quickAction('finance')">
          <div class="action-icon action-icon-gold">
            <a-icon type="account-book" />
          </div>
          <div class="action-label">财务管理</div>
        </div>

        <div class="action-item" @click="quickAction('more')">
          <div class="action-icon action-icon-gray">
            <a-icon type="appstore" />
          </div>
          <div class="action-label">更多功能</div>
        </div>
      </div>
    </div>

    <!-- 业务概览 -->
    <div class="dashboard-overview">
      <div class="section-title">
        <span>业务概览</span>
        <a-button type="link" size="small" @click="viewBusinessReport">详细报表</a-button>
      </div>

      <div class="overview-cards">
        <div class="overview-card">
          <div class="overview-header">
            <span class="overview-title">本月销售</span>
            <a-icon type="line-chart" class="overview-icon" />
          </div>
          <div class="overview-value">{{ formatMoney(stats.monthSales) }}</div>
          <div class="overview-trend">
            <a-icon type="rise" class="trend-up" />
            <span>环比 +15.2%</span>
          </div>
        </div>

        <div class="overview-card">
          <div class="overview-header">
            <span class="overview-title">待处理订单</span>
            <a-icon type="clock-circle" class="overview-icon" />
          </div>
          <div class="overview-value">{{ stats.pendingOrders }}</div>
          <div class="overview-trend">
            <span class="trend-text">需及时处理</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近动态 -->
    <div class="dashboard-activities">
      <div class="section-title">
        <span>最近动态</span>
        <a-button type="link" size="small" @click="viewAllActivities">查看全部</a-button>
      </div>

      <div class="activity-list">
        <div
          v-for="activity in recentActivities"
          :key="activity.id"
          class="activity-item"
          @click="handleActivityClick(activity)"
        >
          <div class="activity-avatar">{{ activity.avatar }}</div>
          <div class="activity-content">
            <div class="activity-title">{{ activity.title }}</div>
            <div class="activity-time">{{ activity.timeText }}</div>
          </div>
          <a-icon type="right" class="activity-arrow" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ResponsiveMixin } from '@/mixins/responsive'
import { MobileList } from '@/components/mobile'
import { getAction } from '@/api/manage'
import * as echarts from 'echarts'

export default {
  name: 'MobileDashboard',
  mixins: [ResponsiveMixin],
  components: {
    MobileList
  },
  
  data() {
    return {
      // 统计数据
      stats: {
        materialCount: 1248,
        orderCount: 23,
        lowStockCount: 5,
        todaySales: 15680,
        monthSales: 456780,
        pendingOrders: 8
      },

      // 最近动态
      recentActivities: [
        {
          id: 1,
          title: '新增销售订单 #SO2024001',
          timeText: '5分钟前',
          avatar: '📋',
          linkUrl: '/mobile/orders/SO2024001'
        },
        {
          id: 2,
          title: '商品库存预警：珐琅杯',
          timeText: '15分钟前',
          avatar: '⚠️',
          linkUrl: '/mobile/inventory'
        },
        {
          id: 3,
          title: '采购订单已到货',
          timeText: '1小时前',
          avatar: '📦',
          linkUrl: '/mobile/purchase'
        }
      ],

      // 用户信息
      userAvatar: '',
      userName: '管理员'
    }
  },

  computed: {
    // 欢迎消息
    welcomeMessage() {
      const hour = new Date().getHours()
      let greeting = '早上好'
      if (hour >= 12 && hour < 18) {
        greeting = '下午好'
      } else if (hour >= 18) {
        greeting = '晚上好'
      }
      return `${greeting}，${this.userName}`
    },

    // 当前日期
    currentDate() {
      const now = new Date()
      const options = {
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      }
      return now.toLocaleDateString('zh-CN', options)
    }
  },

  mounted() {
    this.loadDashboardData()
    this.initUserInfo()
  },

  methods: {
    // 初始化用户信息
    initUserInfo() {
      // 从Vuex或localStorage获取用户信息
      const userInfo = this.$store.getters.userInfo || {}
      this.userName = userInfo.realname || userInfo.username || '管理员'
      this.userAvatar = userInfo.avatar || ''
    },

    // 加载仪表板数据
    async loadDashboardData() {
      try {
        await this.loadStats()
      } catch (error) {
        console.error('加载仪表板数据失败:', error)
        this.$message.error('加载数据失败')
      }
    },

    // 加载统计数据
    async loadStats() {
      try {
        const res = await getAction('/dashboard/stats')
        if (res.success) {
          this.stats = res.result
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },



    // 导航到指定页面
    navigateTo(path) {
      this.$router.push(path)
    },

    // 快捷操作
    quickAction(type) {
      switch (type) {
        case 'pos':
          this.$router.push('/mobile/pos')
          break
        case 'purchase':
          this.$router.push('/mobile/purchase/add')
          break
        case 'inventory':
          this.$router.push('/mobile/inventory/check')
          break
        case 'material':
          this.$router.push('/mobile/material/add')
          break
        case 'report':
          this.$router.push('/mobile/reports')
          break
        case 'customer':
          this.$router.push('/mobile/customers')
          break
        case 'finance':
          this.$router.push('/mobile/finance')
          break
        case 'more':
          this.$router.push('/mobile/functions')
          break
      }
    },

    // 查看全部功能
    viewAllFunctions() {
      this.$router.push('/mobile/functions')
    },

    // 查看业务报表
    viewBusinessReport() {
      this.$router.push('/mobile/reports/business')
    },

    // 处理动态点击
    handleActivityClick(activity) {
      if (activity.linkUrl) {
        this.$router.push(activity.linkUrl)
      }
    },

    // 查看全部动态
    viewAllActivities() {
      this.$router.push('/mobile/activities')
    },

    // 格式化金额
    formatMoney(amount) {
      if (!amount) return '¥0'
      return `¥${Number(amount).toLocaleString()}`
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      const date = new Date(time)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
      return `${Math.floor(diff / 86400000)}天前`
    }
  }
}
</script>

<style lang="less" scoped>

.mobile-dashboard {
  padding: 0;
  background-color: #f7f8fa;
  min-height: 100vh;

  // 顶部欢迎区域
  .dashboard-header {
    background: linear-gradient(135deg, #3B82F6, #1D4ED8);
    padding: 24px 20px 32px;
    color: white;

    .welcome-section {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .welcome-text {
        .greeting {
          font-size: 20px;
          font-weight: 600;
          margin-bottom: 4px;
        }

        .date {
          font-size: 14px;
          opacity: 0.9;
        }
      }

      .user-avatar {
        .ant-avatar {
          border: 2px solid rgba(255, 255, 255, 0.3);
        }
      }
    }
  }

  // 核心数据卡片
  .dashboard-stats {
    padding: 0 20px;
    margin-top: -16px;
    margin-bottom: 24px;

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;

      .stat-card {
        background: white;
        border-radius: 12px;
        padding: 20px 16px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.98);
        }

        &.stat-card-primary {
          background: linear-gradient(135deg, #3B82F6, #1D4ED8);
          color: white;

          .stat-header .stat-title {
            color: rgba(255, 255, 255, 0.9);
          }

          .stat-subtitle {
            color: rgba(255, 255, 255, 0.8);
          }

          .trend-icon {
            color: #10B981;
          }
        }

        &.stat-card-warning {
          border-left: 4px solid #F59E0B;

          .stat-value {
            color: #F59E0B;
          }
        }

        .stat-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .stat-title {
            font-size: 13px;
            color: #6B7280;
            font-weight: 500;
          }

          .stat-icon, .trend-icon {
            font-size: 16px;
            color: #9CA3AF;
          }
        }

        .stat-value {
          font-size: 24px;
          font-weight: 700;
          color: #111827;
          line-height: 1.2;
          margin-bottom: 4px;
        }

        .stat-subtitle {
          font-size: 12px;
          color: #6B7280;
        }
      }
    }
  }

  // 快捷功能区域
  .dashboard-actions {
    padding: 0 20px;
    margin-bottom: 24px;

    .section-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 18px;
      font-weight: 600;
      color: #111827;
      margin-bottom: 16px;
    }

    .action-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 16px 12px;

      .action-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 16px 8px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 1px 6px rgba(0, 0, 0, 0.06);
        cursor: pointer;
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.95);
        }

        .action-icon {
          width: 44px;
          height: 44px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 12px;
          margin-bottom: 8px;

          .anticon {
            font-size: 20px;
            color: white;
          }

          &.action-icon-primary {
            background: linear-gradient(135deg, #3B82F6, #1D4ED8);
          }

          &.action-icon-blue {
            background: linear-gradient(135deg, #06B6D4, #0891B2);
          }

          &.action-icon-orange {
            background: linear-gradient(135deg, #F59E0B, #D97706);
          }

          &.action-icon-green {
            background: linear-gradient(135deg, #10B981, #059669);
          }

          &.action-icon-purple {
            background: linear-gradient(135deg, #8B5CF6, #7C3AED);
          }

          &.action-icon-cyan {
            background: linear-gradient(135deg, #06B6D4, #0891B2);
          }

          &.action-icon-gold {
            background: linear-gradient(135deg, #F59E0B, #D97706);
          }

          &.action-icon-gray {
            background: linear-gradient(135deg, #6B7280, #4B5563);
          }
        }

        .action-label {
          font-size: 12px;
          color: #374151;
          text-align: center;
          font-weight: 500;
        }
      }
    }
  }

  // 业务概览区域
  .dashboard-overview {
    padding: 0 20px;
    margin-bottom: 24px;

    .section-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 18px;
      font-weight: 600;
      color: #111827;
      margin-bottom: 16px;
    }

    .overview-cards {
      display: grid;
      grid-template-columns: 1fr;
      gap: 12px;

      .overview-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 1px 6px rgba(0, 0, 0, 0.06);

        .overview-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .overview-title {
            font-size: 14px;
            color: #6B7280;
            font-weight: 500;
          }

          .overview-icon {
            font-size: 16px;
            color: #9CA3AF;
          }
        }

        .overview-value {
          font-size: 28px;
          font-weight: 700;
          color: #111827;
          margin-bottom: 8px;
        }

        .overview-trend {
          display: flex;
          align-items: center;
          font-size: 13px;

          .trend-up {
            color: #10B981;
            margin-right: 4px;
          }

          .trend-text {
            color: #6B7280;
          }
        }
      }
    }
  }

  // 最近动态区域
  .dashboard-activities {
    padding: 0 20px;
    margin-bottom: 24px;

    .section-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 18px;
      font-weight: 600;
      color: #111827;
      margin-bottom: 16px;
    }

    .activity-list {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 1px 6px rgba(0, 0, 0, 0.06);

      .activity-item {
        display: flex;
        align-items: center;
        padding: 16px 20px;
        border-bottom: 1px solid #F3F4F6;
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:last-child {
          border-bottom: none;
        }

        &:active {
          background-color: #F9FAFB;
        }

        .activity-avatar {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #F3F4F6;
          border-radius: 10px;
          margin-right: 12px;
          font-size: 18px;
        }

        .activity-content {
          flex: 1;

          .activity-title {
            font-size: 14px;
            color: #111827;
            font-weight: 500;
            margin-bottom: 2px;
          }

          .activity-time {
            font-size: 12px;
            color: #6B7280;
          }
        }

        .activity-arrow {
          font-size: 12px;
          color: #D1D5DB;
        }
      }
    }
  }
}
</style>

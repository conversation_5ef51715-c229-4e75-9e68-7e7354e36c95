-- 聆花文化ERP系统配置脚本
-- 基于jshERP现有功能的配置优化方案
-- 执行前请确保已有jshERP基础数据

-- =====================================================
-- 第一部分：商品分类配置
-- =====================================================

-- 1.1 创建聆花产品分类体系
INSERT INTO jsh_material_category (name, parent_id, category_level, sort, remark, tenant_id, delete_flag) VALUES
('聆花艺术臻品', NULL, 1, 1, '高端艺术品系列', 63, '0'),
('聆花非遗文创', NULL, 1, 2, '非遗文化创意产品', 63, '0'),
('聆花手作馆', NULL, 1, 3, '手作体验产品', 63, '0'),
('仿珐琅产品', NULL, 1, 4, '仿珐琅装饰品', 63, '0');

-- 1.2 创建二级分类
INSERT INTO jsh_material_category (name, parent_id, category_level, sort, remark, tenant_id, delete_flag) VALUES
('掐丝珐琅画', (SELECT id FROM jsh_material_category WHERE name='聆花艺术臻品' AND tenant_id=63), 2, 1, '传统掐丝珐琅画作品', 63, '0'),
('高端定制品', (SELECT id FROM jsh_material_category WHERE name='聆花艺术臻品' AND tenant_id=63), 2, 2, '个性化定制艺术品', 63, '0'),
('东方珐琅彩饰品', (SELECT id FROM jsh_material_category WHERE name='聆花非遗文创' AND tenant_id=63), 2, 1, '珐琅彩装饰饰品', 63, '0'),
('文创纪念品', (SELECT id FROM jsh_material_category WHERE name='聆花非遗文创' AND tenant_id=63), 2, 2, '文化纪念品', 63, '0'),
('手作体验套件', (SELECT id FROM jsh_material_category WHERE name='聆花手作馆' AND tenant_id=63), 2, 1, '体验活动材料包', 63, '0'),
('团建课程包', (SELECT id FROM jsh_material_category WHERE name='聆花手作馆' AND tenant_id=63), 2, 2, '团建活动课程', 63, '0'),
('日用装饰品', (SELECT id FROM jsh_material_category WHERE name='仿珐琅产品' AND tenant_id=63), 2, 1, '日常装饰用品', 63, '0'),
('礼品套装', (SELECT id FROM jsh_material_category WHERE name='仿珐琅产品' AND tenant_id=63), 2, 2, '礼品组合套装', 63, '0');

-- =====================================================
-- 第二部分：仓库配置
-- =====================================================

-- 2.1 创建聆花仓库体系
INSERT INTO jsh_depot (name, address, warehousing, tradeType, remark, sort, tenant_id, delete_flag) VALUES
('广州原料仓', '广州市天河区聆花文化园区', 1, '入库,出库,调拨', '存放采购的底胎、画框、配件、包装材料', 1, 63, '0'),
('广州半成品仓', '广州市天河区聆花文化园区', 1, '入库,出库,调拨', '存放广西制作完成但未配饰/装裱的作品', 2, 63, '0'),
('广州成品仓', '广州市天河区聆花文化园区', 1, '入库,出库,调拨', '聆花掐丝珐琅馆仓，存放最终成品', 3, 63, '0'),
('广西生产基地仓', '广西崇左市生产基地', 0, '虚拟仓库', '记录寄给广西基地待加工的底胎', 4, 63, '0'),
('深圳景之蓝仓', '深圳市南山区景之蓝公司', 0, '虚拟仓库', '记录发往景之蓝的产品库存', 5, 63, '0'),
('广州美术馆仓', '广州市越秀区广州美术馆', 0, '虚拟仓库', '代销渠道虚拟仓库', 6, 63, '0'),
('中山纪念堂仓', '广州市越秀区中山纪念堂', 0, '虚拟仓库', '代销渠道虚拟仓库', 7, 63, '0'),
('番禺新华书店仓', '广州市番禺区新华书店', 0, '虚拟仓库', '代销渠道虚拟仓库', 8, 63, '0');

-- =====================================================
-- 第三部分：供应商配置
-- =====================================================

-- 3.1 创建供应商档案
INSERT INTO jsh_supplier (supplier, contacts, phonenum, email, address, type, remark, tenant_id, delete_flag) VALUES
('唐卡铜胎供应商', '张师傅', '13800138001', '<EMAIL>', '西藏拉萨市城关区', '底胎供应商', '专业铜胎制作，质量稳定', 63, '0'),
('阿里巴巴铜底胎商', '李经理', '13800138002', '<EMAIL>', '浙江杭州市余杭区', '底胎供应商', '网络采购平台，品种丰富', 63, '0'),
('玉石底胎供应商', '王师傅', '13800138003', '<EMAIL>', '新疆和田市', '底胎供应商', '天然玉石底胎，高端定制', 63, '0'),
('黑檀木底胎供应商', '赵师傅', '13800138004', '<EMAIL>', '海南海口市', '底胎供应商', '黑檀木底胎，文创系列', 63, '0'),
('花梨木框供应商', '陈师傅', '13800138005', '<EMAIL>', '广东中山市', '装裱供应商', '传统木框制作，工艺精湛', 63, '0'),
('欧式框供应商', '刘经理', '13800138006', '<EMAIL>', '广东佛山市', '装裱供应商', '欧式画框，现代风格', 63, '0'),
('饰品配件供应商', '吴经理', '13800138007', '<EMAIL>', '广东深圳市', '配饰供应商', '珠类、挂扣、挂穗、耳勾等配件', 63, '0'),
('包装材料供应商', '周经理', '13800138008', '<EMAIL>', '广东东莞市', '包装供应商', '包装盒、包装袋等材料', 63, '0'),
('广西生产基地', '陈主任', '13800138009', '<EMAIL>', '广西崇左市江州区', '委外加工商', '掐丝珐琅制作基地，技术成熟', 63, '0');

-- =====================================================
-- 第四部分：客户配置
-- =====================================================

-- 4.1 创建客户档案（使用supplier表的客户功能）
INSERT INTO jsh_supplier (supplier, contacts, phonenum, email, address, type, remark, tenant_id, delete_flag) VALUES
('深圳景之蓝', '赵总', '0755-12345678', '<EMAIL>', '深圳市南山区科技园', '合作伙伴', '长期合作伙伴，供货和制作费结算', 63, '0'),
('广州美术馆', '刘馆长', '020-12345678', '<EMAIL>', '广州市越秀区二沙岛', '代销渠道', '文化场所代销，月度结算', 63, '0'),
('中山纪念堂', '陈经理', '020-87654321', '<EMAIL>', '广州市越秀区东风中路', '代销渠道', '旅游景点代销，季度结算', 63, '0'),
('番禺新华书店', '黄经理', '020-11111111', '<EMAIL>', '广州市番禺区市桥街', '代销渠道', '零售终端代销，月度结算', 63, '0');

-- =====================================================
-- 第五部分：计量单位配置
-- =====================================================

-- 5.1 创建聆花专用计量单位
INSERT INTO jsh_unit (name, remark, tenant_id, delete_flag) VALUES
('幅', '珐琅画计量单位', 63, '0'),
('套', '套装产品计量单位', 63, '0'),
('件', '饰品计量单位', 63, '0'),
('份', '手作套件计量单位', 63, '0'),
('场', '团建活动计量单位', 63, '0'),
('人次', '体验活动计量单位', 63, '0');

-- =====================================================
-- 第六部分：收支项目配置
-- =====================================================

-- 6.1 创建收支项目分类
INSERT INTO jsh_in_out_item (name, type, remark, tenant_id, delete_flag) VALUES
('产品销售收入', '收入', '聆花产品销售收入', 63, '0'),
('手作体验收入', '收入', '手作体验活动收入', 63, '0'),
('团建项目收入', '收入', '团建活动项目收入', 63, '0'),
('咖啡店销售收入', '收入', '印象咖咖啡店收入', 63, '0'),
('渠道代销收入', '收入', '代销渠道销售收入', 63, '0'),
('原料采购支出', '支出', '底胎、釉料、配件采购', 63, '0'),
('委外加工费', '支出', '广西生产基地加工费', 63, '0'),
('员工薪酬支出', '支出', '员工工资和提成', 63, '0'),
('场地租赁费', '支出', '团建场地租赁费用', 63, '0'),
('包装运输费', '支出', '产品包装和物流费用', 63, '0');

-- =====================================================
-- 第七部分：账户配置
-- =====================================================

-- 7.1 创建资金账户
INSERT INTO jsh_account (name, serial_no, initial_amount, current_amount, remark, tenant_id, delete_flag) VALUES
('聆花文化基本户', 'LH001', 0.00, 0.00, '聆花文化企业基本账户', 63, '0'),
('聆花文化一般户', 'LH002', 0.00, 0.00, '聆花文化一般存款账户', 63, '0'),
('微信收款账户', 'LH003', 0.00, 0.00, '微信支付收款账户', 63, '0'),
('支付宝收款账户', 'LH004', 0.00, 0.00, '支付宝收款账户', 63, '0'),
('现金账户', 'LH005', 0.00, 0.00, '现金收支账户', 63, '0'),
('景之蓝合作账户', 'LH006', 0.00, 0.00, '与景之蓝合作专用账户', 63, '0');

-- =====================================================
-- 配置完成提示
-- =====================================================

-- 配置完成后，请执行以下检查：
-- 1. 检查商品分类是否创建成功：SELECT * FROM jsh_material_category WHERE tenant_id = 63;
-- 2. 检查仓库是否创建成功：SELECT * FROM jsh_depot WHERE tenant_id = 63;
-- 3. 检查供应商客户是否创建成功：SELECT * FROM jsh_supplier WHERE tenant_id = 63;
-- 4. 检查计量单位是否创建成功：SELECT * FROM jsh_unit WHERE tenant_id = 63;
-- 5. 检查收支项目是否创建成功：SELECT * FROM jsh_in_out_item WHERE tenant_id = 63;
-- 6. 检查账户是否创建成功：SELECT * FROM jsh_account WHERE tenant_id = 63;

-- 注意：请根据实际的tenant_id修改上述SQL中的tenant_id值（示例中使用63）
